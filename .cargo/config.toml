# 项目级别 Cargo 配置
# 位置: .cargo/config.toml

[source.crates-io]
# 使用国内镜像源加速下载
replace-with = "rsproxy-sparse"

# 中科大镜像源（推荐）
[source.rsproxy-sparse]
registry = "sparse+https://mirrors.ustc.edu.cn/crates.io-index/"

# 构建配置
[build]
# 并行编译任务数
jobs = 4
# 增量编译
incremental = true

# 目标配置 - Linux
[target.x86_64-unknown-linux-gnu]
rustflags = [
    "-C", "target-cpu=native",
    "-C", "opt-level=2",
    "-C", "link-arg=-fuse-ld=lld"
]

# 目标配置 - Windows (交叉编译)
[target.x86_64-pc-windows-gnu]
rustflags = ["-C", "target-cpu=native"]

# 环境变量
[env]
# Tauri 相关环境变量
TAURI_DEBUG = { value = "true", condition = "cfg(debug_assertions)" }
RUST_LOG = { value = "info", condition = "cfg(debug_assertions)" }
RUST_BACKTRACE = { value = "1", condition = "cfg(debug_assertions)" }

# 性能优化
CARGO_PROFILE_DEV_DEBUG = "1"
CARGO_PROFILE_RELEASE_DEBUG = "0"
CARGO_PROFILE_RELEASE_LTO = "thin"
CARGO_PROFILE_RELEASE_CODEGEN_UNITS = "1"

# 别名
[alias]
# Tauri 相关别名
tauri-dev = "tauri dev"
tauri-build = "tauri build"
tauri-check = "check --manifest-path=src-tauri/Cargo.toml"

# 开发别名
dev-check = "check --workspace --all-targets"
dev-test = "test --workspace --all-targets"
dev-clippy = "clippy --workspace --all-targets -- -D warnings"
dev-fmt = "fmt --all -- --check"

# 清理别名
clean-all = "clean"
clean-target = "clean --target-dir target"
