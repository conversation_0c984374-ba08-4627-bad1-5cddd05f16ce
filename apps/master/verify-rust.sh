#!/bin/bash
# Rust 配置验证脚本

echo "🦀 验证 Rust 配置"
echo "=================="

# 加载 Cargo 环境
source $HOME/.cargo/env

echo "✅ Rust 版本: $(rustc --version)"
echo "✅ Cargo 版本: $(cargo --version)"

echo ""
echo "🔧 检查 Cargo 配置..."

# 检查镜像源配置
echo "📦 当前注册表配置:"
if [ -f ~/.cargo/config.toml ]; then
    echo "  全局配置文件存在: ~/.cargo/config.toml"
fi

if [ -f .cargo/config.toml ]; then
    echo "  项目配置文件存在: .cargo/config.toml"
fi

echo ""
echo "🎯 测试基本 Cargo 命令..."

cd src-tauri

# 测试基本命令
echo "测试 cargo --version:"
cargo --version

echo ""
echo "测试 cargo check --version:"
cargo check --version

echo ""
echo "📋 依赖信息:"
echo "Cargo.toml 文件存在: $([ -f Cargo.toml ] && echo '是' || echo '否')"

if [ -f Cargo.toml ]; then
    echo "项目名称: $(grep '^name' Cargo.toml | cut -d'"' -f2)"
    echo "项目版本: $(grep '^version' Cargo.toml | cut -d'"' -f2)"
fi

echo ""
echo "🚀 Rust 配置验证完成！"
