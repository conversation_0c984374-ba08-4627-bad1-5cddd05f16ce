@echo off
echo 微博舆情分析管理节点 - 环境设置脚本
echo =====================================

echo.
echo 1. 检查 Node.js 环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未找到 Node.js，请先安装 Node.js 18.0+
    pause
    exit /b 1
)
echo [成功] Node.js 已安装

echo.
echo 2. 检查 Rust 环境...
rustc --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未找到 Rust，请先安装 Rust 1.70+
    pause
    exit /b 1
)
echo [成功] Rust 已安装

echo.
echo 3. 安装前端依赖...
npm install
if %errorlevel% neq 0 (
    echo [错误] 前端依赖安装失败
    pause
    exit /b 1
)
echo [成功] 前端依赖安装完成

echo.
echo 4. 检查 Tauri CLI...
npm list @tauri-apps/cli >nul 2>&1
if %errorlevel% neq 0 (
    echo [警告] Tauri CLI 未安装，正在安装...
    npm install -g @tauri-apps/cli
)
echo [成功] Tauri CLI 已就绪

echo.
echo 5. 创建环境配置文件...
if not exist .env (
    copy .env.example .env
    echo [成功] 已创建 .env 文件，请根据需要修改配置
) else (
    echo [信息] .env 文件已存在
)

echo.
echo =====================================
echo 环境设置完成！
echo.
echo 下一步：
echo 1. 编辑 .env 文件配置数据库连接
echo 2. 启动 PostgreSQL、Redis 和 RabbitMQ 服务
echo 3. 运行 start_dev.bat 启动开发环境
echo =====================================
pause
