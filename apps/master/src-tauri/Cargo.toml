[package]
name = "weibo-sentiment-master"
version = "0.1.0"
description = "微博舆情分析系统的管理节点"
authors = ["Weibo Sentiment Analysis Team"]
license = "MIT"
repository = "https://github.com/your-org/weibo-sentiment-analysis"
edition = "2021"

[lib]
name = "weibo_sentiment_master"
path = "src/lib.rs"

[[bin]]
name = "weibo-sentiment-master"
path = "src/main.rs"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "1.5", features = [] }

[dependencies]
# Tauri 核心依赖
tauri = { version = "1.5", features = [ "global-shortcut-all",
    "window-hide", "window-close", "window-start-dragging", "shell-open", 
    "os-all", "fs-all", "window-unminimize", "window-unmaximize", 
    "window-show", "window-minimize", "window-maximize", "path-all", 
    "global-shortcut", "notification-all", "system-tray"] }

# 序列化和反序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# 异步运行时
tokio = { version = "1.0", features = ["full"] }

# 数据库连接
sqlx = { version = "0.7", features = [
    "runtime-tokio-rustls", "postgres", "sqlite", "chrono", 
    "uuid", "macros", "migrate", "json"
] }

# Redis 连接
redis = { version = "0.24", features = ["tokio-comp", "connection-manager"] }

# RabbitMQ 连接
lapin = "2.3"

# HTTP 客户端
reqwest = { version = "0.11", features = ["json", "cookies", "gzip", "brotli"] }

# 时间处理
chrono = { version = "0.4", features = ["serde"] }

# UUID 生成
uuid = { version = "1.0", features = ["v4", "serde"] }

# 日志和追踪
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }
tracing-appender = "0.2"

# 错误处理
anyhow = "1.0"
thiserror = "1.0"

# 异步工具
futures = "0.3"
async-trait = "0.1"

# 并发工具
parking_lot = "0.12"
dashmap = "5.5"
once_cell = "1.19"

# 工具库
rand = "0.8"
base64 = "0.21"
url = "2.4"
regex = "1.10"

# 配置管理
config = "0.14"
dotenvy = "0.15"

# 加密
sha2 = "0.10"
bcrypt = "0.15"

# JSON Web Token
jsonwebtoken = "9.2"

# 系统监控
sysinfo = "0.30"

[features]
# this feature is used for production builds or when `devPath` points to the filesystem
# DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]

# 开发特性
default = []
dev = ["tauri/devtools"]

# 性能优化配置
[profile.dev]
# 开发模式优化
opt-level = 0
debug = true
split-debuginfo = "unpacked"
debug-assertions = true
overflow-checks = true
lto = false
panic = "unwind"
incremental = true
codegen-units = 256
rpath = false

[profile.release]
# 发布模式优化
opt-level = 3
debug = false
split-debuginfo = "packed"
debug-assertions = false
overflow-checks = false
lto = "thin"
panic = "abort"
incremental = false
codegen-units = 1
rpath = false
strip = "symbols"

[profile.dev.package."*"]
# 依赖包在开发模式下的优化
opt-level = 2

[profile.test]
# 测试模式配置
opt-level = 1
debug = true
debug-assertions = true
overflow-checks = true
lto = false
panic = "unwind"
incremental = true
codegen-units = 256

[[bin]]
name = "migrate"
path = "src/bin/migrate.rs"

[[bin]]
name = "seed"
path = "src/bin/seed.rs"

[[bin]]
name = "server"
path = "src/bin/server.rs"
