use crate::{
    database::DatabaseManager,
    error::AppR<PERSON>ult,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use sysinfo::{System, SystemExt, CpuExt, DiskExt};

/// 系统监控服务
#[derive(Clone)]
pub struct MonitorService {
    db: Arc<DatabaseManager>,
}

/// 系统状态信息
#[derive(Debug, Serialize, Deserialize)]
pub struct SystemStatus {
    pub cpu_usage: f32,
    pub memory_usage: f64,
    pub disk_usage: f64,
    pub network_io: f64,
    pub uptime: u64,
}

impl MonitorService {
    /// 创建新的监控服务
    pub fn new(db: Arc<DatabaseManager>) -> Self {
        Self { db }
    }

    /// 获取系统状态
    pub async fn get_system_status(&self) -> AppResult<SystemStatus> {
        let mut system = System::new_all();
        system.refresh_all();

        let cpu_usage = system.global_cpu_info().cpu_usage();
        
        let total_memory = system.total_memory();
        let used_memory = system.used_memory();
        let memory_usage = if total_memory > 0 {
            (used_memory as f64 / total_memory as f64) * 100.0
        } else {
            0.0
        };

        let disk_usage = if let Some(disk) = system.disks().first() {
            let total_space = disk.total_space();
            let available_space = disk.available_space();
            if total_space > 0 {
                ((total_space - available_space) as f64 / total_space as f64) * 100.0
            } else {
                0.0
            }
        } else {
            0.0
        };

        let uptime = system.uptime();

        Ok(SystemStatus {
            cpu_usage,
            memory_usage,
            disk_usage,
            network_io: 0.0, // TODO: 实现网络IO监控
            uptime,
        })
    }

    /// 检查系统健康状态
    pub async fn health_check(&self) -> AppResult<bool> {
        // 检查数据库连接
        self.db.health_check().await?;
        
        // 检查系统资源
        let status = self.get_system_status().await?;
        
        // 简单的健康检查逻辑
        let is_healthy = status.cpu_usage < 90.0 
            && status.memory_usage < 90.0 
            && status.disk_usage < 90.0;
        
        Ok(is_healthy)
    }
}
