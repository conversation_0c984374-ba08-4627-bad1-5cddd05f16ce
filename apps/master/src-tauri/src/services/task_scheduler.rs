use crate::{
    database::DatabaseManager,
    error::{AppError, AppResult},
    models::{Task, TaskCreation, TaskStatus, TaskStats, TaskUpdate},
};
use chrono::Utc;
use std::sync::Arc;
use tracing::{error, info, warn};
use uuid::Uuid;

/// 任务调度服务
#[derive(Clone)]
pub struct TaskSchedulerService {
    db: Arc<DatabaseManager>,
}

impl TaskSchedulerService {
    /// 创建新的任务调度服务
    pub fn new(db: Arc<DatabaseManager>) -> Self {
        Self { db }
    }

    /// 创建新任务
    pub async fn create_task(&self, creation: TaskCreation) -> AppResult<Task> {
        info!("创建新任务: {}", creation.name);

        let task_id = Uuid::new_v4();
        let now = Utc::now();

        let task = sqlx::query_as!(
            Task,
            r#"
            INSERT INTO tasks (id, name, task_type, priority, config, max_retries, scheduled_at, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            RETURNING id, name, task_type as "task_type: _", status as "status: _", priority as "priority: _", 
                      node_id, config, progress, result, error_message, retry_count, max_retries, 
                      scheduled_at, started_at, completed_at, created_at, updated_at
            "#,
            task_id,
            creation.name,
            creation.task_type as _,
            creation.priority as _,
            creation.config,
            creation.max_retries.unwrap_or(3),
            creation.scheduled_at,
            now,
            now
        )
        .fetch_one(&*self.db.postgres)
        .await?;

        info!("任务创建成功: {} ({})", task.name, task.id);
        Ok(task)
    }

    /// 获取任务列表
    pub async fn get_tasks(&self, limit: Option<i64>, offset: Option<i64>) -> AppResult<Vec<Task>> {
        let limit = limit.unwrap_or(50);
        let offset = offset.unwrap_or(0);

        let tasks = sqlx::query_as!(
            Task,
            r#"
            SELECT id, name, task_type as "task_type: _", status as "status: _", priority as "priority: _", 
                   node_id, config, progress, result, error_message, retry_count, max_retries, 
                   scheduled_at, started_at, completed_at, created_at, updated_at
            FROM tasks
            ORDER BY created_at DESC
            LIMIT $1 OFFSET $2
            "#,
            limit,
            offset
        )
        .fetch_all(&*self.db.postgres)
        .await?;

        Ok(tasks)
    }

    /// 根据ID获取任务
    pub async fn get_task_by_id(&self, task_id: Uuid) -> AppResult<Option<Task>> {
        let task = sqlx::query_as!(
            Task,
            r#"
            SELECT id, name, task_type as "task_type: _", status as "status: _", priority as "priority: _", 
                   node_id, config, progress, result, error_message, retry_count, max_retries, 
                   scheduled_at, started_at, completed_at, created_at, updated_at
            FROM tasks
            WHERE id = $1
            "#,
            task_id
        )
        .fetch_optional(&*self.db.postgres)
        .await?;

        Ok(task)
    }

    /// 更新任务状态
    pub async fn update_task(&self, update: TaskUpdate) -> AppResult<()> {
        let mut query_parts = vec!["UPDATE tasks SET updated_at = NOW()".to_string()];
        let mut bind_values: Vec<Box<dyn sqlx::postgres::PgArgumentBuffer>> = vec![];

        if let Some(status) = &update.status {
            query_parts.push("status = $1".to_string());
        }

        if let Some(progress) = update.progress {
            query_parts.push(format!("progress = ${}", bind_values.len() + 1));
        }

        if query_parts.len() == 1 {
            return Ok(());
        }

        let query_str = format!("{} WHERE id = ${}", query_parts.join(", "), bind_values.len() + 1);
        
        let rows_affected = sqlx::query(&query_str)
            .bind(update.id)
            .execute(&*self.db.postgres)
            .await?
            .rows_affected();

        if rows_affected == 0 {
            return Err(AppError::Task(format!("任务不存在: {}", update.id)));
        }

        Ok(())
    }

    /// 获取任务统计信息
    pub async fn get_task_stats(&self) -> AppResult<TaskStats> {
        let stats = sqlx::query_as!(
            TaskStats,
            r#"
            SELECT 
                COUNT(*) as total_tasks,
                COUNT(*) FILTER (WHERE status = 'pending') as pending_tasks,
                COUNT(*) FILTER (WHERE status = 'running') as running_tasks,
                COUNT(*) FILTER (WHERE status = 'completed') as completed_tasks,
                COUNT(*) FILTER (WHERE status = 'failed') as failed_tasks,
                CASE 
                    WHEN COUNT(*) > 0 THEN 
                        COUNT(*) FILTER (WHERE status = 'completed')::float / COUNT(*)::float * 100
                    ELSE 0 
                END as success_rate,
                COALESCE(AVG(EXTRACT(EPOCH FROM (completed_at - started_at))), 0) as avg_duration,
                COALESCE(COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '1 hour')::float, 0) as tasks_per_hour
            FROM tasks
            "#
        )
        .fetch_one(&*self.db.postgres)
        .await?;

        Ok(stats)
    }
}
