use crate::{
    database::DatabaseManager,
    error::{AppError, AppResult},
    models::{Node, NodeHeartbeat, NodeRegistration, NodeStats, NodeStatus},
};
use chrono::Utc;
use std::sync::Arc;
use tracing::{error, info, warn};
use uuid::Uuid;

/// 节点管理服务
#[derive(Clone)]
pub struct NodeManagerService {
    db: Arc<DatabaseManager>,
}

impl NodeManagerService {
    /// 创建新的节点管理服务
    pub fn new(db: Arc<DatabaseManager>) -> Self {
        Self { db }
    }

    /// 注册新节点
    pub async fn register_node(&self, registration: NodeRegistration) -> AppResult<Node> {
        info!("注册新节点: {}", registration.name);

        let node_id = Uuid::new_v4();
        let now = Utc::now();

        let node = sqlx::query_as!(
            Node,
            r#"
            INSERT INTO nodes (id, name, node_type, status, host, port, version, capabilities, config, last_heartbeat, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
            RETURNING id, name, node_type as "node_type: _", status as "status: _", host, port, version, capabilities, config, last_heartbeat, created_at, updated_at
            "#,
            node_id,
            registration.name,
            registration.node_type as _,
            NodeStatus::Online as _,
            registration.host,
            registration.port,
            registration.version,
            registration.capabilities,
            serde_json::json!({}),
            now,
            now,
            now
        )
        .fetch_one(&*self.db.postgres)
        .await?;

        info!("节点注册成功: {} ({})", node.name, node.id);
        Ok(node)
    }

    /// 获取所有节点列表
    pub async fn get_nodes(&self) -> AppResult<Vec<Node>> {
        let nodes = sqlx::query_as!(
            Node,
            r#"
            SELECT id, name, node_type as "node_type: _", status as "status: _", host, port, version, capabilities, config, last_heartbeat, created_at, updated_at
            FROM nodes
            ORDER BY created_at DESC
            "#
        )
        .fetch_all(&*self.db.postgres)
        .await?;

        Ok(nodes)
    }

    /// 根据ID获取节点
    pub async fn get_node_by_id(&self, node_id: Uuid) -> AppResult<Option<Node>> {
        let node = sqlx::query_as!(
            Node,
            r#"
            SELECT id, name, node_type as "node_type: _", status as "status: _", host, port, version, capabilities, config, last_heartbeat, created_at, updated_at
            FROM nodes
            WHERE id = $1
            "#,
            node_id
        )
        .fetch_optional(&*self.db.postgres)
        .await?;

        Ok(node)
    }

    /// 更新节点状态
    pub async fn update_node_status(&self, node_id: Uuid, status: NodeStatus) -> AppResult<()> {
        let rows_affected = sqlx::query!(
            "UPDATE nodes SET status = $1, updated_at = $2 WHERE id = $3",
            status as _,
            Utc::now(),
            node_id
        )
        .execute(&*self.db.postgres)
        .await?
        .rows_affected();

        if rows_affected == 0 {
            return Err(AppError::Node(format!("节点不存在: {}", node_id)));
        }

        Ok(())
    }

    /// 处理节点心跳
    pub async fn handle_heartbeat(&self, heartbeat: NodeHeartbeat) -> AppResult<()> {
        // 更新节点最后心跳时间和状态
        let rows_affected = sqlx::query!(
            "UPDATE nodes SET status = $1, last_heartbeat = $2, updated_at = $3 WHERE id = $4",
            heartbeat.status as _,
            heartbeat.timestamp,
            Utc::now(),
            heartbeat.node_id
        )
        .execute(&*self.db.postgres)
        .await?
        .rows_affected();

        if rows_affected == 0 {
            warn!("收到未知节点的心跳: {}", heartbeat.node_id);
            return Err(AppError::Node(format!("节点不存在: {}", heartbeat.node_id)));
        }

        // 存储心跳数据到 Redis 用于监控
        let heartbeat_key = format!("heartbeat:{}", heartbeat.node_id);
        let heartbeat_data = serde_json::to_string(&heartbeat)?;
        
        let mut redis_conn = self.db.redis.get_async_connection().await?;
        redis::cmd("SETEX")
            .arg(&heartbeat_key)
            .arg(300) // 5分钟过期
            .arg(&heartbeat_data)
            .query_async(&mut redis_conn)
            .await?;

        Ok(())
    }

    /// 获取节点统计信息
    pub async fn get_node_stats(&self, node_id: Uuid) -> AppResult<NodeStats> {
        let stats = sqlx::query_as!(
            NodeStats,
            r#"
            SELECT 
                $1 as node_id,
                COUNT(*) as total_tasks,
                COUNT(*) FILTER (WHERE status = 'completed') as completed_tasks,
                COUNT(*) FILTER (WHERE status = 'failed') as failed_tasks,
                CASE 
                    WHEN COUNT(*) > 0 THEN 
                        COUNT(*) FILTER (WHERE status = 'completed')::float / COUNT(*)::float * 100
                    ELSE 0 
                END as success_rate,
                COALESCE(AVG(EXTRACT(EPOCH FROM (completed_at - started_at))), 0) as avg_task_duration,
                COALESCE(EXTRACT(EPOCH FROM (NOW() - MIN(created_at))), 0) as uptime,
                COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '24 hours') as last_24h_tasks
            FROM tasks 
            WHERE node_id = $1
            "#,
            node_id
        )
        .fetch_one(&*self.db.postgres)
        .await?;

        Ok(stats)
    }

    /// 删除节点
    pub async fn delete_node(&self, node_id: Uuid) -> AppResult<()> {
        let rows_affected = sqlx::query!(
            "DELETE FROM nodes WHERE id = $1",
            node_id
        )
        .execute(&*self.db.postgres)
        .await?
        .rows_affected();

        if rows_affected == 0 {
            return Err(AppError::Node(format!("节点不存在: {}", node_id)));
        }

        info!("节点已删除: {}", node_id);
        Ok(())
    }

    /// 检查离线节点
    pub async fn check_offline_nodes(&self) -> AppResult<Vec<Uuid>> {
        let offline_threshold = Utc::now() - chrono::Duration::minutes(5);
        
        let offline_nodes = sqlx::query!(
            "SELECT id FROM nodes WHERE last_heartbeat < $1 AND status != 'offline'",
            offline_threshold
        )
        .fetch_all(&*self.db.postgres)
        .await?;

        let mut offline_node_ids = Vec::new();
        for node in offline_nodes {
            offline_node_ids.push(node.id);
            self.update_node_status(node.id, NodeStatus::Offline).await?;
        }

        if !offline_node_ids.is_empty() {
            warn!("发现 {} 个离线节点", offline_node_ids.len());
        }

        Ok(offline_node_ids)
    }
}
