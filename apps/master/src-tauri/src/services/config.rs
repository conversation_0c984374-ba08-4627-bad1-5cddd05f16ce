use crate::error::{AppError, AppResult};
use serde::{Deserialize, Serialize};
use std::env;

/// 应用配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppConfig {
    pub database_url: String,
    pub redis_url: String,
    pub rabbitmq_url: String,
    pub log_level: String,
    pub max_concurrent_tasks: i32,
    pub task_timeout: i32,
    pub heartbeat_interval: i32,
}

/// 配置服务
#[derive(Clone)]
pub struct ConfigService {
    config: AppConfig,
}

impl ConfigService {
    /// 创建新的配置服务
    pub fn new() -> AppResult<Self> {
        let config = Self::load_config()?;
        Ok(Self { config })
    }

    /// 加载配置
    fn load_config() -> AppResult<AppConfig> {
        // 尝试加载 .env 文件
        if let Err(_) = dotenvy::dotenv() {
            // .env 文件不存在或加载失败，使用环境变量
        }

        let database_url = env::var("DATABASE_URL")
            .unwrap_or_else(|_| "postgresql://postgres:password@localhost:5432/weibo_sentiment".to_string());
        
        let redis_url = env::var("REDIS_URL")
            .unwrap_or_else(|_| "redis://localhost:6379".to_string());
        
        let rabbitmq_url = env::var("RABBITMQ_URL")
            .unwrap_or_else(|_| "amqp://guest:guest@localhost:5672".to_string());
        
        let log_level = env::var("LOG_LEVEL")
            .unwrap_or_else(|_| "info".to_string());
        
        let max_concurrent_tasks = env::var("MAX_CONCURRENT_TASKS")
            .unwrap_or_else(|_| "10".to_string())
            .parse()
            .map_err(|e| AppError::Config(format!("无效的 MAX_CONCURRENT_TASKS: {}", e)))?;
        
        let task_timeout = env::var("TASK_TIMEOUT")
            .unwrap_or_else(|_| "3600".to_string())
            .parse()
            .map_err(|e| AppError::Config(format!("无效的 TASK_TIMEOUT: {}", e)))?;
        
        let heartbeat_interval = env::var("HEARTBEAT_INTERVAL")
            .unwrap_or_else(|_| "30".to_string())
            .parse()
            .map_err(|e| AppError::Config(format!("无效的 HEARTBEAT_INTERVAL: {}", e)))?;

        Ok(AppConfig {
            database_url,
            redis_url,
            rabbitmq_url,
            log_level,
            max_concurrent_tasks,
            task_timeout,
            heartbeat_interval,
        })
    }

    /// 获取应用配置
    pub async fn get_app_config(&self) -> AppResult<&AppConfig> {
        Ok(&self.config)
    }

    /// 更新配置
    pub async fn update_config(&mut self, new_config: AppConfig) -> AppResult<()> {
        self.config = new_config;
        Ok(())
    }
}
