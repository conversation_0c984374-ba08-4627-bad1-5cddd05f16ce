pub mod commands;
pub mod database;
pub mod error;
pub mod events;
pub mod models;
pub mod services;

use crate::{
    commands::*,
    database::DatabaseManager,
    services::*,
};
use std::sync::Arc;
use tauri::{Manager, SystemTray, SystemTrayEvent, SystemTrayMenu, SystemTrayMenuItem};
use tracing::{error, info};

/// 应用程序状态
pub struct AppState {
    pub db: Arc<DatabaseManager>,
    pub node_manager: NodeManagerService,
    pub task_scheduler: TaskSchedulerService,
    pub data_processor: DataProcessorService,
    pub monitor: MonitorService,
    pub config: ConfigService,
}

impl AppState {
    /// 创建新的应用程序状态
    pub async fn new() -> Result<Self, Box<dyn std::error::Error>> {
        // 加载配置
        let config = ConfigService::new()?;
        let app_config = config.get_app_config().await?;

        // 初始化数据库连接
        let db = Arc::new(
            DatabaseManager::new(
                &app_config.database_url,
                &app_config.redis_url,
                &app_config.rabbitmq_url,
            )
            .await?,
        );

        // 运行数据库迁移
        db.migrate().await?;

        // 初始化服务
        let node_manager = NodeManagerService::new(db.clone());
        let task_scheduler = TaskSchedulerService::new(db.clone());
        let data_processor = DataProcessorService::new(db.clone());
        let monitor = MonitorService::new(db.clone());

        Ok(Self {
            db,
            node_manager,
            task_scheduler,
            data_processor,
            monitor,
            config,
        })
    }
}

/// 创建系统托盘
fn create_system_tray() -> SystemTray {
    let menu = SystemTrayMenu::new()
        .add_item(SystemTrayMenuItem::new("显示主窗口", "show"))
        .add_native_item(SystemTrayMenuItem::Separator)
        .add_item(SystemTrayMenuItem::new("退出", "quit"));

    SystemTray::new().with_menu(menu)
}

/// 处理系统托盘事件
fn handle_system_tray_event(app: &tauri::AppHandle, event: SystemTrayEvent) {
    match event {
        SystemTrayEvent::LeftClick {
            position: _,
            size: _,
            ..
        } => {
            if let Some(window) = app.get_window("main") {
                let _ = window.show();
                let _ = window.set_focus();
            }
        }
        SystemTrayEvent::MenuItemClick { id, .. } => match id.as_str() {
            "show" => {
                if let Some(window) = app.get_window("main") {
                    let _ = window.show();
                    let _ = window.set_focus();
                }
            }
            "quit" => {
                app.exit(0);
            }
            _ => {}
        },
        _ => {}
    }
}

/// 构建 Tauri 应用程序
pub async fn build_app() -> Result<tauri::App, Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt()
        .with_env_filter(tracing_subscriber::EnvFilter::from_default_env())
        .init();

    info!("正在启动微博舆情分析管理节点...");

    // 初始化应用程序状态
    let app_state = AppState::new().await?;

    // 构建 Tauri 应用程序
    let app = tauri::Builder::default()
        .manage(app_state.node_manager.clone())
        .manage(app_state.task_scheduler.clone())
        .manage(app_state.data_processor.clone())
        .manage(app_state.monitor.clone())
        .manage(app_state.config.clone())
        .system_tray(create_system_tray())
        .on_system_tray_event(handle_system_tray_event)
        .invoke_handler(tauri::generate_handler![
            // 节点管理命令
            register_node,
            get_node_list,
            get_node_detail,
            get_node_stats,
            delete_node,
            update_node_config,
            check_offline_nodes,
            // 任务管理命令
            // create_task,
            // get_task_list,
            // get_task_detail,
            // update_task_status,
            // cancel_task,
            // retry_task,
            // 仪表板命令
            // get_dashboard_data,
            // get_system_status,
            // 配置管理命令
            // get_app_config,
            // update_app_config,
        ])
        .setup(|app| {
            info!("应用程序初始化完成");
            Ok(())
        })
        .build(tauri::generate_context!())?;

    info!("微博舆情分析管理节点启动成功");
    Ok(app)
}
