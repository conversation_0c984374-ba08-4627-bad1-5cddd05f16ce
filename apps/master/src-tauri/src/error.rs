use serde::{Deserialize, Serialize};
use thiserror::Error;

/// 应用程序错误类型
#[derive(Erro<PERSON>, Debug, Serialize, Deserialize)]
pub enum AppError {
    #[error("数据库错误: {0}")]
    Database(String),

    #[error("Redis 错误: {0}")]
    Redis(String),

    #[error("RabbitMQ 错误: {0}")]
    RabbitMQ(String),

    #[error("HTTP 请求错误: {0}")]
    Http(String),

    #[error("序列化错误: {0}")]
    Serialization(String),

    #[error("配置错误: {0}")]
    Config(String),

    #[error("认证错误: {0}")]
    Authentication(String),

    #[error("授权错误: {0}")]
    Authorization(String),

    #[error("验证错误: {0}")]
    Validation(String),

    #[error("节点错误: {0}")]
    Node(String),

    #[error("任务错误: {0}")]
    Task(String),

    #[error("系统错误: {0}")]
    System(String),

    #[error("未知错误: {0}")]
    Unknown(String),
}

impl From<sqlx::Error> for AppError {
    fn from(err: sqlx::Error) -> Self {
        AppError::Database(err.to_string())
    }
}

impl From<redis::RedisError> for AppError {
    fn from(err: redis::RedisError) -> Self {
        AppError::Redis(err.to_string())
    }
}

impl From<lapin::Error> for AppError {
    fn from(err: lapin::Error) -> Self {
        AppError::RabbitMQ(err.to_string())
    }
}

impl From<reqwest::Error> for AppError {
    fn from(err: reqwest::Error) -> Self {
        AppError::Http(err.to_string())
    }
}

impl From<serde_json::Error> for AppError {
    fn from(err: serde_json::Error) -> Self {
        AppError::Serialization(err.to_string())
    }
}

impl From<config::ConfigError> for AppError {
    fn from(err: config::ConfigError) -> Self {
        AppError::Config(err.to_string())
    }
}

impl From<anyhow::Error> for AppError {
    fn from(err: anyhow::Error) -> Self {
        AppError::Unknown(err.to_string())
    }
}

/// 应用程序结果类型
pub type AppResult<T> = Result<T, AppError>;

/// Tauri 命令结果类型
pub type CommandResult<T> = Result<T, String>;

/// 将 AppError 转换为 Tauri 命令错误
impl From<AppError> for String {
    fn from(err: AppError) -> Self {
        err.to_string()
    }
}
