use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

/// 节点状态
#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "node_status", rename_all = "lowercase")]
pub enum NodeStatus {
    Online,
    Offline,
    Busy,
    Error,
    Maintenance,
}

/// 节点类型
#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "node_type", rename_all = "lowercase")]
pub enum NodeType {
    Crawler,
    Processor,
    Storage,
}

/// 爬虫节点模型
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Node {
    pub id: Uuid,
    pub name: String,
    pub node_type: NodeType,
    pub status: NodeStatus,
    pub host: String,
    pub port: i32,
    pub version: String,
    pub capabilities: serde_json::Value,
    pub config: serde_json::Value,
    pub last_heartbeat: DateTime<Utc>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 节点注册请求
#[derive(Debug, Serialize, Deserialize)]
pub struct NodeRegistration {
    pub name: String,
    pub node_type: NodeType,
    pub host: String,
    pub port: i32,
    pub version: String,
    pub capabilities: serde_json::Value,
}

/// 节点心跳数据
#[derive(Debug, Serialize, Deserialize)]
pub struct NodeHeartbeat {
    pub node_id: Uuid,
    pub status: NodeStatus,
    pub cpu_usage: f64,
    pub memory_usage: f64,
    pub disk_usage: f64,
    pub active_tasks: i32,
    pub completed_tasks: i32,
    pub failed_tasks: i32,
    pub timestamp: DateTime<Utc>,
}

/// 节点统计信息
#[derive(Debug, Serialize, Deserialize)]
pub struct NodeStats {
    pub node_id: Uuid,
    pub total_tasks: i64,
    pub completed_tasks: i64,
    pub failed_tasks: i64,
    pub success_rate: f64,
    pub avg_task_duration: f64,
    pub uptime: i64,
    pub last_24h_tasks: i64,
}

/// 节点配置更新
#[derive(Debug, Serialize, Deserialize)]
pub struct NodeConfigUpdate {
    pub node_id: Uuid,
    pub config: serde_json::Value,
}

impl Default for NodeStatus {
    fn default() -> Self {
        NodeStatus::Offline
    }
}

impl Default for NodeType {
    fn default() -> Self {
        NodeType::Crawler
    }
}
