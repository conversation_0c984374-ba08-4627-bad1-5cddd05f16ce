use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

/// 任务状态
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "task_status", rename_all = "lowercase")]
pub enum TaskStatus {
    Pending,
    Running,
    Completed,
    Failed,
    Cancelled,
    Paused,
}

/// 任务类型
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "task_type", rename_all = "lowercase")]
pub enum TaskType {
    UserCrawl,
    PostCrawl,
    CommentCrawl,
    SearchCrawl,
    TrendingCrawl,
    DataProcess,
    DataAnalysis,
}

/// 任务优先级
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "task_priority", rename_all = "lowercase")]
pub enum TaskPriority {
    Low,
    Normal,
    High,
    Critical,
}

/// 任务模型
#[derive(Debug, <PERSON><PERSON>, Serial<PERSON>, Deserialize, FromRow)]
pub struct Task {
    pub id: Uuid,
    pub name: String,
    pub task_type: TaskType,
    pub status: TaskStatus,
    pub priority: TaskPriority,
    pub node_id: Option<Uuid>,
    pub config: serde_json::Value,
    pub progress: f64,
    pub result: Option<serde_json::Value>,
    pub error_message: Option<String>,
    pub retry_count: i32,
    pub max_retries: i32,
    pub scheduled_at: Option<DateTime<Utc>>,
    pub started_at: Option<DateTime<Utc>>,
    pub completed_at: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 任务创建请求
#[derive(Debug, Serialize, Deserialize)]
pub struct TaskCreation {
    pub name: String,
    pub task_type: TaskType,
    pub priority: TaskPriority,
    pub config: serde_json::Value,
    pub max_retries: Option<i32>,
    pub scheduled_at: Option<DateTime<Utc>>,
}

/// 任务更新请求
#[derive(Debug, Serialize, Deserialize)]
pub struct TaskUpdate {
    pub id: Uuid,
    pub status: Option<TaskStatus>,
    pub progress: Option<f64>,
    pub result: Option<serde_json::Value>,
    pub error_message: Option<String>,
}

/// 任务统计信息
#[derive(Debug, Serialize, Deserialize)]
pub struct TaskStats {
    pub total_tasks: i64,
    pub pending_tasks: i64,
    pub running_tasks: i64,
    pub completed_tasks: i64,
    pub failed_tasks: i64,
    pub success_rate: f64,
    pub avg_duration: f64,
    pub tasks_per_hour: f64,
}

/// 任务队列信息
#[derive(Debug, Serialize, Deserialize)]
pub struct TaskQueue {
    pub priority: TaskPriority,
    pub count: i64,
    pub oldest_task: Option<DateTime<Utc>>,
}

impl Default for TaskStatus {
    fn default() -> Self {
        TaskStatus::Pending
    }
}

impl Default for TaskPriority {
    fn default() -> Self {
        TaskPriority::Normal
    }
}
