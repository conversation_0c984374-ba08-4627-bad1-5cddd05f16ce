use serde::{Deserialize, Serialize};

/// 分析数据模型
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serial<PERSON>, Deserialize)]
pub struct AnalyticsData {
    pub sentiment_distribution: SentimentDistribution,
    pub keyword_frequency: Vec<KeywordFrequency>,
    pub trend_analysis: TrendAnalysis,
    pub user_activity: UserActivity,
}

/// 情感分布
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serial<PERSON>, Deserialize)]
pub struct SentimentDistribution {
    pub positive: i64,
    pub negative: i64,
    pub neutral: i64,
}

/// 关键词频率
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct KeywordFrequency {
    pub keyword: String,
    pub frequency: i64,
    pub sentiment_score: f64,
}

/// 趋势分析
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct TrendAnalysis {
    pub time_series: Vec<TimeSeriesPoint>,
    pub growth_rate: f64,
    pub peak_time: Option<String>,
}

/// 时间序列数据点
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct TimeSeriesPoint {
    pub timestamp: String,
    pub value: i64,
    pub sentiment_score: f64,
}

/// 用户活动统计
#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct UserActivity {
    pub total_users: i64,
    pub active_users: i64,
    pub new_users: i64,
    pub top_influencers: Vec<Influencer>,
}

/// 影响者信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Influencer {
    pub user_id: String,
    pub username: String,
    pub followers_count: i64,
    pub influence_score: f64,
}
