use crate::error::{AppError, AppResult};
use redis::{Client, Connection};
use tracing::{info, warn};

/// 创建 Redis 客户端
pub fn create_client(redis_url: &str) -> AppResult<Client> {
    info!("正在连接到 Redis...");
    
    let client = Client::open(redis_url)
        .map_err(|e| AppError::Redis(format!("创建 Redis 客户端失败: {}", e)))?;

    info!("Redis 客户端创建成功");
    Ok(client)
}

/// Redis 健康检查
pub async fn health_check(client: &Client) -> AppResult<()> {
    let mut conn = client.get_async_connection().await
        .map_err(|e| AppError::Redis(format!("Redis 连接失败: {}", e)))?;

    let result: String = redis::cmd("PING")
        .query_async(&mut conn)
        .await
        .map_err(|e| AppError::Redis(format!("Redis PING 失败: {}", e)))?;

    if result != "PONG" {
        return Err(AppError::Redis("Redis 健康检查失败".to_string()));
    }

    Ok(())
}
