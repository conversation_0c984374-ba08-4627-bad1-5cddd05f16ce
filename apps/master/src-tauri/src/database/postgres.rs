use crate::error::{AppError, AppResult};
use sqlx::{postgres::PgPoolOptions, Pool, Postgres};
use tracing::{info, warn};

/// 创建 PostgreSQL 连接池
pub async fn create_pool(database_url: &str) -> AppResult<Pool<Postgres>> {
    info!("正在连接到 PostgreSQL 数据库...");
    
    let pool = PgPoolOptions::new()
        .max_connections(20)
        .min_connections(5)
        .acquire_timeout(std::time::Duration::from_secs(30))
        .idle_timeout(std::time::Duration::from_secs(600))
        .max_lifetime(std::time::Duration::from_secs(1800))
        .connect(database_url)
        .await
        .map_err(|e| AppError::Database(format!("连接数据库失败: {}", e)))?;

    info!("PostgreSQL 数据库连接成功");
    Ok(pool)
}

/// 运行数据库迁移
pub async fn run_migrations(pool: &Pool<Postgres>) -> AppResult<()> {
    info!("正在运行数据库迁移...");
    
    sqlx::migrate!("./migrations")
        .run(pool)
        .await
        .map_err(|e| AppError::Database(format!("数据库迁移失败: {}", e)))?;

    info!("数据库迁移完成");
    Ok(())
}

/// 健康检查
pub async fn health_check(pool: &Pool<Postgres>) -> AppResult<()> {
    let result = sqlx::query("SELECT 1")
        .fetch_one(pool)
        .await
        .map_err(|e| AppError::Database(format!("数据库健康检查失败: {}", e)))?;

    if result.is_empty() {
        return Err(AppError::Database("数据库健康检查返回空结果".to_string()));
    }

    Ok(())
}

/// 获取数据库统计信息
pub async fn get_stats(pool: &Pool<Postgres>) -> AppResult<DatabaseStats> {
    let stats = sqlx::query_as!(
        DatabaseStats,
        r#"
        SELECT 
            (SELECT COUNT(*) FROM nodes) as total_nodes,
            (SELECT COUNT(*) FROM tasks) as total_tasks,
            (SELECT COUNT(*) FROM tasks WHERE status = 'running') as running_tasks,
            (SELECT COUNT(*) FROM tasks WHERE status = 'pending') as pending_tasks
        "#
    )
    .fetch_one(pool)
    .await
    .map_err(|e| AppError::Database(format!("获取数据库统计信息失败: {}", e)))?;

    Ok(stats)
}

/// 数据库统计信息
#[derive(Debug, serde::Serialize)]
pub struct DatabaseStats {
    pub total_nodes: Option<i64>,
    pub total_tasks: Option<i64>,
    pub running_tasks: Option<i64>,
    pub pending_tasks: Option<i64>,
}
