pub mod postgres;
pub mod redis;
pub mod rabbitmq;

use crate::error::AppResult;
use sqlx::{Pool, Postgres};
use std::sync::Arc;

/// 数据库连接池
pub type DbPool = Pool<Postgres>;

/// 数据库管理器
#[derive(Clone)]
pub struct DatabaseManager {
    pub postgres: Arc<DbPool>,
    pub redis: Arc<redis::Client>,
    pub rabbitmq: Arc<lapin::Connection>,
}

impl DatabaseManager {
    /// 创建新的数据库管理器
    pub async fn new(
        postgres_url: &str,
        redis_url: &str,
        rabbitmq_url: &str,
    ) -> AppResult<Self> {
        let postgres = Arc::new(postgres::create_pool(postgres_url).await?);
        let redis = Arc::new(redis::create_client(redis_url)?);
        let rabbitmq = Arc::new(rabbitmq::create_connection(rabbitmq_url).await?);

        Ok(Self {
            postgres,
            redis,
            rabbitmq,
        })
    }

    /// 运行数据库迁移
    pub async fn migrate(&self) -> AppResult<()> {
        postgres::run_migrations(&self.postgres).await
    }

    /// 健康检查
    pub async fn health_check(&self) -> AppResult<()> {
        postgres::health_check(&self.postgres).await?;
        redis::health_check(&self.redis).await?;
        rabbitmq::health_check(&self.rabbitmq).await?;
        Ok(())
    }
}
