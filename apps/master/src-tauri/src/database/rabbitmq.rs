use crate::error::{AppError, AppResult};
use lapin::{Connection, ConnectionProperties};
use tracing::{info, warn};

/// 创建 RabbitMQ 连接
pub async fn create_connection(rabbitmq_url: &str) -> AppResult<Connection> {
    info!("正在连接到 RabbitMQ...");
    
    let connection = Connection::connect(rabbitmq_url, ConnectionProperties::default())
        .await
        .map_err(|e| AppError::RabbitMQ(format!("连接 RabbitMQ 失败: {}", e)))?;

    info!("RabbitMQ 连接成功");
    Ok(connection)
}

/// RabbitMQ 健康检查
pub async fn health_check(connection: &Connection) -> AppResult<()> {
    if connection.status().connected() {
        Ok(())
    } else {
        Err(AppError::RabbitMQ("RabbitMQ 连接已断开".to_string()))
    }
}
