use serde::{Deserialize, Serialize};
use uuid::Uuid;

/// 系统事件类型
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum SystemEvent {
    NodeOnline { node_id: Uuid, node_name: String },
    NodeOffline { node_id: Uuid, node_name: String },
    TaskCompleted { task_id: Uuid, task_name: String },
    TaskFailed { task_id: Uuid, task_name: String, error: String },
    SystemAlert { level: AlertLevel, message: String },
}

/// 告警级别
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum AlertLevel {
    Info,
    Warning,
    Error,
    Critical,
}

/// 事件处理器
pub struct EventHandler;

impl EventHandler {
    /// 处理系统事件
    pub async fn handle_event(event: SystemEvent) {
        match event {
            SystemEvent::NodeOnline { node_id, node_name } => {
                tracing::info!("节点上线: {} ({})", node_name, node_id);
            }
            SystemEvent::NodeOffline { node_id, node_name } => {
                tracing::warn!("节点离线: {} ({})", node_name, node_id);
            }
            SystemEvent::TaskCompleted { task_id, task_name } => {
                tracing::info!("任务完成: {} ({})", task_name, task_id);
            }
            SystemEvent::TaskFailed { task_id, task_name, error } => {
                tracing::error!("任务失败: {} ({}) - {}", task_name, task_id, error);
            }
            SystemEvent::SystemAlert { level, message } => {
                match level {
                    AlertLevel::Info => tracing::info!("系统信息: {}", message),
                    AlertLevel::Warning => tracing::warn!("系统警告: {}", message),
                    AlertLevel::Error => tracing::error!("系统错误: {}", message),
                    AlertLevel::Critical => tracing::error!("系统严重错误: {}", message),
                }
            }
        }
    }
}
