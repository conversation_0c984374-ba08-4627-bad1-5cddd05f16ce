use crate::{
    error::CommandResult,
    models::{Node, NodeRegistration, NodeStats},
    services::NodeManagerService,
};
use tauri::State;
use uuid::Uuid;

/// 注册新节点
#[tauri::command]
pub async fn register_node(
    node_manager: State<'_, NodeManagerService>,
    registration: NodeRegistration,
) -> CommandResult<Node> {
    node_manager
        .register_node(registration)
        .await
        .map_err(|e| e.to_string())
}

/// 获取所有节点列表
#[tauri::command]
pub async fn get_node_list(
    node_manager: State<'_, NodeManagerService>,
) -> CommandResult<Vec<Node>> {
    node_manager
        .get_nodes()
        .await
        .map_err(|e| e.to_string())
}

/// 根据ID获取节点详情
#[tauri::command]
pub async fn get_node_detail(
    node_manager: State<'_, NodeManagerService>,
    node_id: String,
) -> CommandResult<Option<Node>> {
    let uuid = Uuid::parse_str(&node_id)
        .map_err(|e| format!("无效的节点ID: {}", e))?;
    
    node_manager
        .get_node_by_id(uuid)
        .await
        .map_err(|e| e.to_string())
}

/// 获取节点统计信息
#[tauri::command]
pub async fn get_node_stats(
    node_manager: State<'_, NodeManagerService>,
    node_id: String,
) -> CommandResult<NodeStats> {
    let uuid = Uuid::parse_str(&node_id)
        .map_err(|e| format!("无效的节点ID: {}", e))?;
    
    node_manager
        .get_node_stats(uuid)
        .await
        .map_err(|e| e.to_string())
}

/// 删除节点
#[tauri::command]
pub async fn delete_node(
    node_manager: State<'_, NodeManagerService>,
    node_id: String,
) -> CommandResult<()> {
    let uuid = Uuid::parse_str(&node_id)
        .map_err(|e| format!("无效的节点ID: {}", e))?;
    
    node_manager
        .delete_node(uuid)
        .await
        .map_err(|e| e.to_string())
}

/// 更新节点配置
#[tauri::command]
pub async fn update_node_config(
    node_manager: State<'_, NodeManagerService>,
    node_id: String,
    config: serde_json::Value,
) -> CommandResult<()> {
    let uuid = Uuid::parse_str(&node_id)
        .map_err(|e| format!("无效的节点ID: {}", e))?;
    
    // TODO: 实现节点配置更新逻辑
    // 这里需要通过消息队列向节点发送配置更新命令
    
    Ok(())
}

/// 检查离线节点
#[tauri::command]
pub async fn check_offline_nodes(
    node_manager: State<'_, NodeManagerService>,
) -> CommandResult<Vec<String>> {
    let offline_nodes = node_manager
        .check_offline_nodes()
        .await
        .map_err(|e| e.to_string())?;
    
    Ok(offline_nodes.into_iter().map(|id| id.to_string()).collect())
}
