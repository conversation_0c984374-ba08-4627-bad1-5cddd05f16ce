#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/home/<USER>/demo/apps/master/node_modules/.pnpm/vitest@1.6.1_@types+node@24.1.0_@vitest+ui@0.34.7_jsdom@26.1.0/node_modules/vitest/node_modules:/home/<USER>/demo/apps/master/node_modules/.pnpm/vitest@1.6.1_@types+node@24.1.0_@vitest+ui@0.34.7_jsdom@26.1.0/node_modules:/home/<USER>/demo/apps/master/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/home/<USER>/demo/apps/master/node_modules/.pnpm/vitest@1.6.1_@types+node@24.1.0_@vitest+ui@0.34.7_jsdom@26.1.0/node_modules/vitest/node_modules:/home/<USER>/demo/apps/master/node_modules/.pnpm/vitest@1.6.1_@types+node@24.1.0_@vitest+ui@0.34.7_jsdom@26.1.0/node_modules:/home/<USER>/demo/apps/master/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../vitest@1.6.1_@types+node@24.1.0_@vitest+ui@0.34.7_jsdom@26.1.0/node_modules/vitest/vitest.mjs" "$@"
else
  exec node  "$basedir/../../../../../../vitest@1.6.1_@types+node@24.1.0_@vitest+ui@0.34.7_jsdom@26.1.0/node_modules/vitest/vitest.mjs" "$@"
fi
