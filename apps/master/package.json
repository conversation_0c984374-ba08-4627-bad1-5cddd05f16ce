{"name": "weibo-sentiment-master", "version": "0.1.0", "description": "微博舆情分析系统的管理节点", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:debug": "playwright test --debug", "test:all": "npm run test:run && npm run test:e2e", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"@tauri-apps/api": "^1.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "zustand": "^4.4.0", "recharts": "^2.8.0", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-progress": "^1.0.3", "class-variance-authority": "^0.7.0", "date-fns": "^2.30.0", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4"}, "devDependencies": {"@tauri-apps/cli": "^1.5.0", "@types/node": "^24.1.0", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.2.2", "vite": "^5.0.8", "vitest": "^1.0.4", "@vitest/ui": "^0.34.6", "@vitest/coverage-v8": "^3.2.4", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@playwright/test": "^1.40.0", "jsdom": "^26.1.0", "tailwindcss-animate": "^1.0.7"}}