import { Routes, Route } from "react-router-dom";
import { Layout } from "@/components/Layout";
import { Dashboard } from "@/components/Dashboard";
import { NodeManager } from "@/components/NodeManager";
import { TaskManager } from "@/components/TaskManager";
import { DataAnalysis } from "@/components/DataAnalysis";
import { SystemMonitor } from "@/components/SystemMonitor";

function App() {
  return (
    <Layout>
      <Routes>
        <Route path="/" element={<Dashboard />} />
        <Route path="/nodes" element={<NodeManager />} />
        <Route path="/tasks" element={<TaskManager />} />
        <Route path="/analysis" element={<DataAnalysis />} />
        <Route path="/monitor" element={<SystemMonitor />} />
      </Routes>
    </Layout>
  );
}

export default App;
