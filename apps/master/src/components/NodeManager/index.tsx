import { useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useNodeStore } from "@/stores/useNodeStore";
import { Plus, Trash2, Settings } from "lucide-react";

export function NodeManager() {
  const { nodes, fetchNodes, deleteNode, loading, error } = useNodeStore();

  useEffect(() => {
    fetchNodes();
  }, [fetchNodes]);

  const handleDeleteNode = async (nodeId: string) => {
    if (confirm("确定要删除这个节点吗？")) {
      try {
        await deleteNode(nodeId);
      } catch (error) {
        console.error("删除节点失败:", error);
      }
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "online":
        return "bg-green-500";
      case "busy":
        return "bg-yellow-500";
      case "offline":
        return "bg-red-500";
      case "error":
        return "bg-red-600";
      case "maintenance":
        return "bg-blue-500";
      default:
        return "bg-gray-500";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "online":
        return "在线";
      case "busy":
        return "繁忙";
      case "offline":
        return "离线";
      case "error":
        return "错误";
      case "maintenance":
        return "维护中";
      default:
        return "未知";
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case "crawler":
        return "爬虫节点";
      case "processor":
        return "处理节点";
      case "storage":
        return "存储节点";
      default:
        return "未知类型";
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">节点管理</h1>
          <p className="text-muted-foreground mt-2">
            管理和监控分布式爬虫节点
          </p>
        </div>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          添加节点
        </Button>
      </div>

      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <p className="text-red-600">{error}</p>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>节点列表</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">加载中...</p>
            </div>
          ) : nodes.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">暂无节点数据</p>
            </div>
          ) : (
            <div className="space-y-4">
              {nodes.map((node) => (
                <div
                  key={node.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-accent/50 transition-colors"
                >
                  <div className="flex items-center space-x-4">
                    <div
                      className={`w-3 h-3 rounded-full ${getStatusColor(node.status)}`}
                    />
                    <div>
                      <h3 className="font-medium">{node.name}</h3>
                      <p className="text-sm text-muted-foreground">
                        {node.host}:{node.port} • {getTypeText(node.node_type)}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        版本: {node.version} • 最后心跳: {new Date(node.last_heartbeat).toLocaleString()}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 text-xs rounded-full text-white ${getStatusColor(node.status)}`}>
                      {getStatusText(node.status)}
                    </span>
                    <Button variant="ghost" size="icon">
                      <Settings className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleDeleteNode(node.id)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
