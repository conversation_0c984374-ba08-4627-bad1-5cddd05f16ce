import { NavLink } from "react-router-dom";
import { 
  LayoutDashboard, 
  Server, 
  ListTodo, 
  BarChart3, 
  Monitor,
  Settings
} from "lucide-react";
import { cn } from "@/utils/cn";

const navigation = [
  { name: "仪表板", href: "/", icon: LayoutDashboard },
  { name: "节点管理", href: "/nodes", icon: Server },
  { name: "任务管理", href: "/tasks", icon: ListTodo },
  { name: "数据分析", href: "/analysis", icon: BarChart3 },
  { name: "系统监控", href: "/monitor", icon: Monitor },
  { name: "系统设置", href: "/settings", icon: Settings },
];

export function Sidebar() {
  return (
    <div className="w-64 bg-card border-r border-border">
      <div className="p-6">
        <h1 className="text-xl font-bold text-foreground">
          微博舆情分析
        </h1>
        <p className="text-sm text-muted-foreground mt-1">
          管理节点
        </p>
      </div>
      
      <nav className="px-4 space-y-1">
        {navigation.map((item) => (
          <NavLink
            key={item.name}
            to={item.href}
            className={({ isActive }) =>
              cn(
                "flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",
                isActive
                  ? "bg-primary text-primary-foreground"
                  : "text-muted-foreground hover:text-foreground hover:bg-accent"
              )
            }
          >
            <item.icon className="mr-3 h-5 w-5" />
            {item.name}
          </NavLink>
        ))}
      </nav>
    </div>
  );
}
