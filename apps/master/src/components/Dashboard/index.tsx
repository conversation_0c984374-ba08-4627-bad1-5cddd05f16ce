import { useEffect } from "react";
import { Card, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { useNodeStore } from "@/stores/useNodeStore";
import { Server, Activity, CheckCircle, AlertCircle } from "lucide-react";

export function Dashboard() {
  const { nodes, fetchNodes } = useNodeStore();

  useEffect(() => {
    fetchNodes();
  }, [fetchNodes]);

  const onlineNodes = nodes.filter(node => node.status === "online").length;
  const offlineNodes = nodes.filter(node => node.status === "offline").length;
  const busyNodes = nodes.filter(node => node.status === "busy").length;

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-foreground">仪表板</h1>
        <p className="text-muted-foreground mt-2">
          微博舆情分析系统总览
        </p>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总节点数</CardTitle>
            <Server className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{nodes.length}</div>
            <p className="text-xs text-muted-foreground">
              系统中的所有节点
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">在线节点</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{onlineNodes}</div>
            <p className="text-xs text-muted-foreground">
              正常运行的节点
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">繁忙节点</CardTitle>
            <Activity className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{busyNodes}</div>
            <p className="text-xs text-muted-foreground">
              正在执行任务的节点
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">离线节点</CardTitle>
            <AlertCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{offlineNodes}</div>
            <p className="text-xs text-muted-foreground">
              无法连接的节点
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 节点状态列表 */}
      <Card>
        <CardHeader>
          <CardTitle>节点状态</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {nodes.length === 0 ? (
              <p className="text-muted-foreground text-center py-8">
                暂无节点数据
              </p>
            ) : (
              nodes.map((node) => (
                <div
                  key={node.id}
                  className="flex items-center justify-between p-4 border rounded-lg"
                >
                  <div className="flex items-center space-x-4">
                    <div
                      className={`w-3 h-3 rounded-full ${
                        node.status === "online"
                          ? "bg-green-500"
                          : node.status === "busy"
                          ? "bg-yellow-500"
                          : "bg-red-500"
                      }`}
                    />
                    <div>
                      <h3 className="font-medium">{node.name}</h3>
                      <p className="text-sm text-muted-foreground">
                        {node.host}:{node.port}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium capitalize">
                      {node.status}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {node.node_type}
                    </p>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
