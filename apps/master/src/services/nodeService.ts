import { invoke } from "@tauri-apps/api/tauri";
import { Node, NodeRegistration, NodeStats } from "@/types";

export class NodeService {
  async getNodeList(): Promise<Node[]> {
    return await invoke("get_node_list");
  }

  async getNodeDetail(nodeId: string): Promise<Node | null> {
    return await invoke("get_node_detail", { nodeId });
  }

  async registerNode(registration: NodeRegistration): Promise<Node> {
    return await invoke("register_node", { registration });
  }

  async deleteNode(nodeId: string): Promise<void> {
    return await invoke("delete_node", { nodeId });
  }

  async getNodeStats(nodeId: string): Promise<NodeStats> {
    return await invoke("get_node_stats", { nodeId });
  }

  async updateNodeConfig(nodeId: string, config: Record<string, any>): Promise<void> {
    return await invoke("update_node_config", { nodeId, config });
  }

  async checkOfflineNodes(): Promise<string[]> {
    return await invoke("check_offline_nodes");
  }
}

export const nodeService = new NodeService();
