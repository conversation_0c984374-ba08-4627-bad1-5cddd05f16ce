import { create } from "zustand";
import { devtools } from "zustand/middleware";
import { Node, NodeStats, NodeRegistration } from "@/types";
import { nodeService } from "@/services/nodeService";

interface NodeState {
  nodes: Node[];
  selectedNode: Node | null;
  nodeStats: Record<string, NodeStats>;
  loading: boolean;
  error: string | null;
  
  // Actions
  fetchNodes: () => Promise<void>;
  selectNode: (node: Node | null) => void;
  registerNode: (registration: NodeRegistration) => Promise<void>;
  deleteNode: (nodeId: string) => Promise<void>;
  fetchNodeStats: (nodeId: string) => Promise<void>;
  updateNodeConfig: (nodeId: string, config: Record<string, any>) => Promise<void>;
  checkOfflineNodes: () => Promise<void>;
  clearError: () => void;
}

export const useNodeStore = create<NodeState>()(
  devtools(
    (set, get) => ({
      nodes: [],
      selectedNode: null,
      nodeStats: {},
      loading: false,
      error: null,

      fetchNodes: async () => {
        set({ loading: true, error: null });
        try {
          const nodes = await nodeService.getNodeList();
          set({ nodes, loading: false });
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : "获取节点列表失败", 
            loading: false 
          });
        }
      },

      selectNode: (node) => {
        set({ selectedNode: node });
      },

      registerNode: async (registration) => {
        set({ loading: true, error: null });
        try {
          const newNode = await nodeService.registerNode(registration);
          const { nodes } = get();
          set({ 
            nodes: [newNode, ...nodes], 
            loading: false 
          });
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : "注册节点失败", 
            loading: false 
          });
          throw error;
        }
      },

      deleteNode: async (nodeId) => {
        set({ loading: true, error: null });
        try {
          await nodeService.deleteNode(nodeId);
          const { nodes, selectedNode } = get();
          const updatedNodes = nodes.filter(node => node.id !== nodeId);
          const newSelectedNode = selectedNode?.id === nodeId ? null : selectedNode;
          set({ 
            nodes: updatedNodes, 
            selectedNode: newSelectedNode,
            loading: false 
          });
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : "删除节点失败", 
            loading: false 
          });
          throw error;
        }
      },

      fetchNodeStats: async (nodeId) => {
        try {
          const stats = await nodeService.getNodeStats(nodeId);
          const { nodeStats } = get();
          set({ 
            nodeStats: { 
              ...nodeStats, 
              [nodeId]: stats 
            } 
          });
        } catch (error) {
          console.error("获取节点统计信息失败:", error);
        }
      },

      updateNodeConfig: async (nodeId, config) => {
        set({ loading: true, error: null });
        try {
          await nodeService.updateNodeConfig(nodeId, config);
          set({ loading: false });
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : "更新节点配置失败", 
            loading: false 
          });
          throw error;
        }
      },

      checkOfflineNodes: async () => {
        try {
          const offlineNodeIds = await nodeService.checkOfflineNodes();
          if (offlineNodeIds.length > 0) {
            // 刷新节点列表以更新状态
            get().fetchNodes();
          }
        } catch (error) {
          console.error("检查离线节点失败:", error);
        }
      },

      clearError: () => {
        set({ error: null });
      },
    }),
    {
      name: "node-store",
    }
  )
);
