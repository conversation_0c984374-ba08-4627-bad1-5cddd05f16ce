// 节点相关类型
export interface Node {
  id: string;
  name: string;
  node_type: NodeType;
  status: NodeStatus;
  host: string;
  port: number;
  version: string;
  capabilities: Record<string, any>;
  config: Record<string, any>;
  last_heartbeat: string;
  created_at: string;
  updated_at: string;
}

export enum NodeType {
  Crawler = "crawler",
  Processor = "processor",
  Storage = "storage",
}

export enum NodeStatus {
  Online = "online",
  Offline = "offline",
  Busy = "busy",
  Error = "error",
  Maintenance = "maintenance",
}

export interface NodeRegistration {
  name: string;
  node_type: NodeType;
  host: string;
  port: number;
  version: string;
  capabilities: Record<string, any>;
}

export interface NodeStats {
  node_id: string;
  total_tasks: number;
  completed_tasks: number;
  failed_tasks: number;
  success_rate: number;
  avg_task_duration: number;
  uptime: number;
  last_24h_tasks: number;
}

// 任务相关类型
export interface Task {
  id: string;
  name: string;
  task_type: TaskType;
  status: TaskStatus;
  priority: TaskPriority;
  node_id?: string;
  config: Record<string, any>;
  progress: number;
  result?: Record<string, any>;
  error_message?: string;
  retry_count: number;
  max_retries: number;
  scheduled_at?: string;
  started_at?: string;
  completed_at?: string;
  created_at: string;
  updated_at: string;
}

export enum TaskType {
  UserCrawl = "user_crawl",
  PostCrawl = "post_crawl",
  CommentCrawl = "comment_crawl",
  SearchCrawl = "search_crawl",
  TrendingCrawl = "trending_crawl",
  DataProcess = "data_process",
  DataAnalysis = "data_analysis",
}

export enum TaskStatus {
  Pending = "pending",
  Running = "running",
  Completed = "completed",
  Failed = "failed",
  Cancelled = "cancelled",
  Paused = "paused",
}

export enum TaskPriority {
  Low = "low",
  Normal = "normal",
  High = "high",
  Critical = "critical",
}

export interface TaskCreation {
  name: string;
  task_type: TaskType;
  priority: TaskPriority;
  config: Record<string, any>;
  max_retries?: number;
  scheduled_at?: string;
}

export interface TaskStats {
  total_tasks: number;
  pending_tasks: number;
  running_tasks: number;
  completed_tasks: number;
  failed_tasks: number;
  success_rate: number;
  avg_duration: number;
  tasks_per_hour: number;
}

// 仪表板数据类型
export interface DashboardData {
  node_stats: {
    total_nodes: number;
    online_nodes: number;
    offline_nodes: number;
    busy_nodes: number;
  };
  task_stats: TaskStats;
  system_stats: {
    cpu_usage: number;
    memory_usage: number;
    disk_usage: number;
    network_io: number;
  };
  recent_activities: Activity[];
}

export interface Activity {
  id: string;
  type: ActivityType;
  title: string;
  description: string;
  timestamp: string;
  severity: ActivitySeverity;
}

export enum ActivityType {
  NodeOnline = "node_online",
  NodeOffline = "node_offline",
  TaskCompleted = "task_completed",
  TaskFailed = "task_failed",
  SystemAlert = "system_alert",
}

export enum ActivitySeverity {
  Info = "info",
  Warning = "warning",
  Error = "error",
  Success = "success",
}

// 配置类型
export interface AppConfig {
  database_url: string;
  redis_url: string;
  rabbitmq_url: string;
  log_level: string;
  max_concurrent_tasks: number;
  task_timeout: number;
  heartbeat_interval: number;
}

// API 响应类型
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}
