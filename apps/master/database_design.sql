-- Master节点数据库设计
-- 基于PostgreSQL 15+

-- 1. 爬虫节点管理表
CREATE TABLE crawler_nodes (
    id BIGSERIAL PRIMARY KEY,
    node_id VARCHAR(50) UNIQUE NOT NULL,
    node_name VARCHAR(100) NOT NULL,
    node_type INTEGER DEFAULT 1, -- 1:爬虫节点 2:管理节点
    ip_address INET,
    port INTEGER,
    status INTEGER DEFAULT 1, -- 1:在线 2:离线 3:维护中 4:异常
    version VARCHAR(20),
    capabilities TEXT[], -- 节点能力列表
    max_concurrent_tasks INTEGER DEFAULT 10,
    current_tasks INTEGER DEFAULT 0,
    cpu_usage DECIMAL(5,2) DEFAULT 0.00,
    memory_usage DECIMAL(5,2) DEFAULT 0.00,
    disk_usage DECIMAL(5,2) DEFAULT 0.00,
    network_latency INTEGER DEFAULT 0, -- 网络延迟(ms)
    success_rate DECIMAL(5,2) DEFAULT 0.00,
    total_tasks_completed BIGINT DEFAULT 0,
    last_heartbeat TIMESTAMP WITH TIME ZONE,
    registered_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. 全局任务管理表
CREATE TABLE global_tasks (
    id BIGSERIAL PRIMARY KEY,
    task_id VARCHAR(50) UNIQUE NOT NULL,
    task_name VARCHAR(200) NOT NULL,
    task_type INTEGER NOT NULL, -- 1:用户爬取 2:内容爬取 3:评论爬取 4:话题爬取
    priority INTEGER DEFAULT 5, -- 1-10优先级
    status INTEGER DEFAULT 0, -- 0:待分配 1:已分配 2:执行中 3:已完成 4:失败 5:已取消
    assigned_node_id VARCHAR(50),
    target_config JSONB NOT NULL, -- 爬取目标配置
    crawl_config JSONB, -- 爬取参数配置
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    estimated_duration INTEGER, -- 预估执行时间(秒)
    actual_duration INTEGER, -- 实际执行时间(秒)
    progress DECIMAL(5,2) DEFAULT 0.00, -- 执行进度百分比
    result_summary JSONB, -- 执行结果摘要
    error_message TEXT,
    created_by VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    assigned_at TIMESTAMP WITH TIME ZONE,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. 任务执行日志表
CREATE TABLE task_execution_logs (
    id BIGSERIAL PRIMARY KEY,
    task_id VARCHAR(50) NOT NULL,
    node_id VARCHAR(50) NOT NULL,
    log_level INTEGER NOT NULL, -- 1:DEBUG 2:INFO 3:WARN 4:ERROR
    message TEXT NOT NULL,
    context_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. 系统配置表
CREATE TABLE system_configs (
    id BIGSERIAL PRIMARY KEY,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value JSONB NOT NULL,
    config_type VARCHAR(50) NOT NULL, -- global, node_specific, task_specific
    description TEXT,
    is_encrypted BOOLEAN DEFAULT FALSE,
    created_by VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. 用户管理表
CREATE TABLE system_users (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    role INTEGER DEFAULT 1, -- 1:普通用户 2:管理员 3:超级管理员
    permissions TEXT[], -- 权限列表
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP WITH TIME ZONE,
    login_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. 操作审计日志表
CREATE TABLE audit_logs (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES system_users(id),
    operation VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id VARCHAR(100),
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. 系统告警表
CREATE TABLE system_alerts (
    id BIGSERIAL PRIMARY KEY,
    alert_type INTEGER NOT NULL, -- 1:节点异常 2:任务失败 3:系统资源 4:数据质量
    severity INTEGER NOT NULL, -- 1:低 2:中 3:高 4:紧急
    title VARCHAR(200) NOT NULL,
    description TEXT,
    source_type VARCHAR(50), -- node, task, system
    source_id VARCHAR(100),
    status INTEGER DEFAULT 1, -- 1:未处理 2:处理中 3:已解决 4:已忽略
    assigned_to VARCHAR(100),
    resolved_by VARCHAR(100),
    resolved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 8. 数据统计缓存表
CREATE TABLE statistics_cache (
    id BIGSERIAL PRIMARY KEY,
    cache_key VARCHAR(200) UNIQUE NOT NULL,
    cache_data JSONB NOT NULL,
    cache_type VARCHAR(50) NOT NULL, -- dashboard, event, topic, user
    time_range VARCHAR(50), -- 1h, 24h, 7d, 30d
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_crawler_nodes_status ON crawler_nodes(status);
CREATE INDEX idx_crawler_nodes_last_heartbeat ON crawler_nodes(last_heartbeat);
CREATE INDEX idx_global_tasks_status ON global_tasks(status);
CREATE INDEX idx_global_tasks_priority ON global_tasks(priority DESC);
CREATE INDEX idx_global_tasks_assigned_node ON global_tasks(assigned_node_id);
CREATE INDEX idx_global_tasks_created_at ON global_tasks(created_at);
CREATE INDEX idx_task_logs_task_id ON task_execution_logs(task_id);
CREATE INDEX idx_task_logs_created_at ON task_execution_logs(created_at);
CREATE INDEX idx_system_configs_key ON system_configs(config_key);
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);
CREATE INDEX idx_alerts_status ON system_alerts(status);
CREATE INDEX idx_alerts_severity ON system_alerts(severity);
CREATE INDEX idx_alerts_created_at ON system_alerts(created_at);
CREATE INDEX idx_stats_cache_key ON statistics_cache(cache_key);
CREATE INDEX idx_stats_cache_expires ON statistics_cache(expires_at);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_crawler_nodes_updated_at
    BEFORE UPDATE ON crawler_nodes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_global_tasks_updated_at
    BEFORE UPDATE ON global_tasks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_configs_updated_at
    BEFORE UPDATE ON system_configs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_users_updated_at
    BEFORE UPDATE ON system_users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_alerts_updated_at
    BEFORE UPDATE ON system_alerts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
