# Rust 代码格式化配置
edition = "2021"
max_width = 100
hard_tabs = false
tab_spaces = 4
newline_style = "Unix"
use_small_heuristics = "Default"

# 导入和模块
reorder_imports = true
reorder_modules = true
imports_layout = "Mixed"
merge_imports = false

# 代码简化
remove_nested_parens = true
merge_derives = true
use_try_shorthand = true
use_field_init_shorthand = true
force_explicit_abi = true

# 单行配置
empty_item_single_line = true
struct_lit_single_line = true
fn_single_line = false
where_single_line = false

# 注释和文档
normalize_comments = true
normalize_doc_attributes = true
wrap_comments = true
comment_width = 80

# 控制流
control_brace_style = "AlwaysSameLine"
brace_style = "SameLineWhere"

# 其他
trailing_comma = "Vertical"
match_block_trailing_comma = false
