# Cargo 全局配置文件
# 位置: ~/.cargo/config.toml

[source.crates-io]
# 使用国内镜像源加速下载（可选）
# replace-with = "rsproxy-sparse"

# 中科大镜像源
[source.rsproxy-sparse]
registry = "sparse+https://mirrors.ustc.edu.cn/crates.io-index/"

# 清华大学镜像源（备选）
[source.tuna]
registry = "https://mirrors.tuna.tsinghua.edu.cn/git/crates.io-index.git"

# 字节跳动镜像源（备选）
[source.rsproxy]
registry = "https://rsproxy.cn/crates.io-index"

# 构建配置
[build]
# 并行编译任务数（根据CPU核心数调整）
jobs = 4

# 目标配置
[target.x86_64-unknown-linux-gnu]
# 链接器配置
linker = "clang"
rustflags = ["-C", "link-arg=-fuse-ld=lld"]

# 网络配置
[http]
# 网络超时设置
timeout = 30
# 低速限制
low-speed-limit = 10
# 重试次数
retry = 3

# 注册表配置
[registry]
default = "crates-io"
token = ""

# 别名配置
[alias]
# 常用命令别名
b = "build"
c = "check"
t = "test"
r = "run"
rr = "run --release"
br = "build --release"
cr = "clippy"
fmt = "fmt --all"
clean-all = "clean && cargo update"

# 环境变量
[env]
# 设置 Rust 日志级别
RUST_LOG = "info"
# 启用彩色输出
CARGO_TERM_COLOR = "always"
