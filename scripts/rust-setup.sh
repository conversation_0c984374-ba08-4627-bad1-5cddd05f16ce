#!/bin/bash
# Rust 开发环境设置脚本

set -e

echo "🦀 Rust 开发环境设置脚本"
echo "=========================="

# 加载 Cargo 环境
source $HOME/.cargo/env

echo "✅ Rust 版本: $(rustc --version)"
echo "✅ Cargo 版本: $(cargo --version)"

echo ""
echo "🔧 安装开发工具..."

# 安装基础工具
echo "安装 clippy 和 rustfmt..."
rustup component add clippy rustfmt

# 安装 Tauri CLI
echo "安装 Tauri CLI..."
if ! command -v cargo-tauri &> /dev/null; then
    cargo install tauri-cli
else
    echo "Tauri CLI 已安装"
fi

# 安装其他有用的工具
echo "安装其他开发工具..."
cargo install --list | grep -q "cargo-watch" || cargo install cargo-watch
cargo install --list | grep -q "cargo-edit" || cargo install cargo-edit
cargo install --list | grep -q "cargo-outdated" || cargo install cargo-outdated

echo ""
echo "🎯 验证安装..."

# 验证工具
echo "验证 clippy: $(cargo clippy --version)"
echo "验证 rustfmt: $(cargo fmt --version)"
echo "验证 Tauri CLI: $(cargo tauri --version 2>/dev/null || echo '安装中...')"

echo ""
echo "📝 创建开发配置文件..."

# 创建 rustfmt 配置
cat > rustfmt.toml << EOF
# Rust 代码格式化配置
edition = "2021"
max_width = 100
hard_tabs = false
tab_spaces = 4
newline_style = "Unix"
use_small_heuristics = "Default"
reorder_imports = true
reorder_modules = true
remove_nested_parens = true
merge_derives = true
use_try_shorthand = true
use_field_init_shorthand = true
force_explicit_abi = true
empty_item_single_line = true
struct_lit_single_line = true
fn_single_line = false
where_single_line = false
imports_layout = "Mixed"
merge_imports = false
EOF

# 创建 clippy 配置
cat > clippy.toml << EOF
# Clippy 配置文件
avoid-breaking-exported-api = false
msrv = "1.70"
EOF

echo ""
echo "🚀 设置完成！"
echo ""
echo "可用的命令："
echo "  cargo b          # 构建项目"
echo "  cargo c          # 检查代码"
echo "  cargo t          # 运行测试"
echo "  cargo r          # 运行项目"
echo "  cargo cr         # 运行 clippy"
echo "  cargo fmt        # 格式化代码"
echo "  cargo tauri dev  # 启动 Tauri 开发环境"
echo ""
echo "开发工作流："
echo "  1. cargo check   # 快速检查语法"
echo "  2. cargo clippy  # 代码质量检查"
echo "  3. cargo fmt     # 代码格式化"
echo "  4. cargo test    # 运行测试"
echo "  5. cargo build   # 构建项目"
echo ""
