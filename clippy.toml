# Clippy 配置文件
# 最低支持的 Rust 版本
msrv = "1.70"

# 避免破坏导出的 API
avoid-breaking-exported-api = false

# 认知复杂度阈值
cognitive-complexity-threshold = 30

# 类型复杂度阈值
type-complexity-threshold = 250

# 单个表达式的最大行数
single-char-lifetime-names-threshold = 4

# 过多参数的阈值
too-many-arguments-threshold = 7

# 过多行数的阈值
too-many-lines-threshold = 100

# 琐碎复制的最大大小
trivial-copy-size-limit = 128

# 枚举变体名称阈值
enum-variant-name-threshold = 3

# 字面量表示的最大位数
literal-representation-threshold = 1024

# 允许的脚本
allowed-scripts = ["Latin"]
